"""
مسارات التقارير المالية
Financial reports routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, make_response
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy import func, and_, or_

from models import db, Invoice, Transaction, Customer, Category
from utils import format_currency, format_date_arabic

# إنشاء Blueprint
reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/')
@login_required
def reports_dashboard():
    """لوحة تحكم التقارير"""
    
    # التحقق من الصلاحيات
    if current_user.role == 'viewer' and current_user.role != 'admin':
        # المشاهدون يمكنهم رؤية التقارير فقط
        pass
    
    return render_template('reports/dashboard.html')

@reports_bp.route('/profit-loss')
@login_required
def profit_loss():
    """تقرير الأرباح والخسائر"""
    
    # معاملات التصفية
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    
    # التواريخ الافتراضية (الشهر الحالي)
    if not date_from:
        date_from = datetime.now().replace(day=1).strftime('%Y-%m-%d')
    if not date_to:
        date_to = datetime.now().strftime('%Y-%m-%d')
    
    try:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
    except ValueError:
        flash('التواريخ المدخلة غير صحيحة', 'error')
        return redirect(url_for('reports.reports_dashboard'))
    
    # الإيرادات (الفواتير المدفوعة)
    revenue_query = db.session.query(func.sum(Invoice.total_amount)).filter(
        Invoice.status == 'paid',
        Invoice.invoice_date >= date_from_obj,
        Invoice.invoice_date <= date_to_obj
    )
    total_revenue = revenue_query.scalar() or Decimal('0')
    
    # تفصيل الإيرادات حسب العملاء
    revenue_by_customer = db.session.query(
        Customer.name,
        func.sum(Invoice.total_amount).label('total')
    ).join(Invoice).filter(
        Invoice.status == 'paid',
        Invoice.invoice_date >= date_from_obj,
        Invoice.invoice_date <= date_to_obj
    ).group_by(Customer.id, Customer.name).order_by(func.sum(Invoice.total_amount).desc()).all()
    
    # المصروفات
    expenses_query = db.session.query(func.sum(Transaction.amount)).filter(
        Transaction.type == 'expense',
        Transaction.transaction_date >= date_from_obj,
        Transaction.transaction_date <= date_to_obj
    )
    total_expenses = expenses_query.scalar() or Decimal('0')
    
    # تفصيل المصروفات حسب الفئات
    expenses_by_category = db.session.query(
        Category.name,
        func.sum(Transaction.amount).label('total')
    ).join(Transaction).filter(
        Transaction.type == 'expense',
        Transaction.transaction_date >= date_from_obj,
        Transaction.transaction_date <= date_to_obj
    ).group_by(Category.id, Category.name).order_by(func.sum(Transaction.amount).desc()).all()
    
    # حساب صافي الربح
    net_profit = total_revenue - total_expenses
    profit_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0
    
    report_data = {
        'date_from': date_from,
        'date_to': date_to,
        'total_revenue': total_revenue,
        'total_expenses': total_expenses,
        'net_profit': net_profit,
        'profit_margin': profit_margin,
        'revenue_by_customer': revenue_by_customer,
        'expenses_by_category': expenses_by_category
    }
    
    return render_template('reports/profit_loss.html', **report_data)

@reports_bp.route('/cash-flow')
@login_required
def cash_flow():
    """تقرير التدفق النقدي"""
    
    # معاملات التصفية
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    
    # التواريخ الافتراضية (الشهر الحالي)
    if not date_from:
        date_from = datetime.now().replace(day=1).strftime('%Y-%m-%d')
    if not date_to:
        date_to = datetime.now().strftime('%Y-%m-%d')
    
    try:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
    except ValueError:
        flash('التواريخ المدخلة غير صحيحة', 'error')
        return redirect(url_for('reports.reports_dashboard'))
    
    # التدفقات النقدية الداخلة (المقبوضات)
    cash_inflows = db.session.query(
        Transaction.transaction_date,
        Transaction.description,
        Transaction.amount,
        Customer.name.label('customer_name')
    ).outerjoin(Customer).filter(
        Transaction.type == 'income',
        Transaction.transaction_date >= date_from_obj,
        Transaction.transaction_date <= date_to_obj
    ).order_by(Transaction.transaction_date.desc()).all()
    
    # التدفقات النقدية الخارجة (المدفوعات)
    cash_outflows = db.session.query(
        Transaction.transaction_date,
        Transaction.description,
        Transaction.amount,
        Category.name.label('category_name')
    ).outerjoin(Category).filter(
        Transaction.type == 'expense',
        Transaction.transaction_date >= date_from_obj,
        Transaction.transaction_date <= date_to_obj
    ).order_by(Transaction.transaction_date.desc()).all()
    
    # إجمالي التدفقات
    total_inflows = sum(flow.amount for flow in cash_inflows)
    total_outflows = sum(flow.amount for flow in cash_outflows)
    net_cash_flow = total_inflows - total_outflows
    
    report_data = {
        'date_from': date_from,
        'date_to': date_to,
        'cash_inflows': cash_inflows,
        'cash_outflows': cash_outflows,
        'total_inflows': total_inflows,
        'total_outflows': total_outflows,
        'net_cash_flow': net_cash_flow
    }
    
    return render_template('reports/cash_flow.html', **report_data)

@reports_bp.route('/sales-summary')
@login_required
def sales_summary():
    """ملخص المبيعات"""
    
    # معاملات التصفية
    period = request.args.get('period', 'month')  # month, quarter, year
    year = request.args.get('year', datetime.now().year, type=int)
    
    # بناء الاستعلام حسب الفترة
    if period == 'month':
        # مبيعات شهرية
        sales_data = db.session.query(
            func.extract('month', Invoice.invoice_date).label('period'),
            func.count(Invoice.id).label('invoice_count'),
            func.sum(Invoice.total_amount).label('total_amount')
        ).filter(
            Invoice.status == 'paid',
            func.extract('year', Invoice.invoice_date) == year
        ).group_by(func.extract('month', Invoice.invoice_date)).all()
        
        # أسماء الشهور
        month_names = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]
        
        # تنظيم البيانات
        formatted_data = []
        for i in range(1, 13):
            month_data = next((item for item in sales_data if item.period == i), None)
            formatted_data.append({
                'period': month_names[i-1],
                'invoice_count': month_data.invoice_count if month_data else 0,
                'total_amount': month_data.total_amount if month_data else Decimal('0')
            })
    
    elif period == 'quarter':
        # مبيعات ربع سنوية
        sales_data = db.session.query(
            func.extract('quarter', Invoice.invoice_date).label('period'),
            func.count(Invoice.id).label('invoice_count'),
            func.sum(Invoice.total_amount).label('total_amount')
        ).filter(
            Invoice.status == 'paid',
            func.extract('year', Invoice.invoice_date) == year
        ).group_by(func.extract('quarter', Invoice.invoice_date)).all()
        
        # تنظيم البيانات
        formatted_data = []
        for i in range(1, 5):
            quarter_data = next((item for item in sales_data if item.period == i), None)
            formatted_data.append({
                'period': f'الربع {i}',
                'invoice_count': quarter_data.invoice_count if quarter_data else 0,
                'total_amount': quarter_data.total_amount if quarter_data else Decimal('0')
            })
    
    else:  # year
        # مبيعات سنوية
        sales_data = db.session.query(
            func.extract('year', Invoice.invoice_date).label('period'),
            func.count(Invoice.id).label('invoice_count'),
            func.sum(Invoice.total_amount).label('total_amount')
        ).filter(
            Invoice.status == 'paid'
        ).group_by(func.extract('year', Invoice.invoice_date)).order_by(
            func.extract('year', Invoice.invoice_date).desc()
        ).limit(5).all()
        
        # تنظيم البيانات
        formatted_data = []
        for item in sales_data:
            formatted_data.append({
                'period': str(int(item.period)),
                'invoice_count': item.invoice_count,
                'total_amount': item.total_amount
            })
    
    # أفضل العملاء
    top_customers = db.session.query(
        Customer.name,
        func.count(Invoice.id).label('invoice_count'),
        func.sum(Invoice.total_amount).label('total_amount')
    ).join(Invoice).filter(
        Invoice.status == 'paid',
        func.extract('year', Invoice.invoice_date) == year
    ).group_by(Customer.id, Customer.name).order_by(
        func.sum(Invoice.total_amount).desc()
    ).limit(10).all()
    
    report_data = {
        'period': period,
        'year': year,
        'sales_data': formatted_data,
        'top_customers': top_customers
    }
    
    return render_template('reports/sales_summary.html', **report_data)

@reports_bp.route('/outstanding-invoices')
@login_required
def outstanding_invoices():
    """تقرير الفواتير المستحقة"""
    
    # الفواتير المرسلة وغير المدفوعة
    outstanding = db.session.query(Invoice).join(Customer).filter(
        Invoice.status.in_(['sent', 'overdue'])
    ).order_by(Invoice.due_date.asc()).all()
    
    # تصنيف حسب فترة التأخير
    current_date = datetime.now().date()
    categorized_invoices = {
        'current': [],      # غير مستحقة بعد
        'overdue_30': [],   # متأخرة 1-30 يوم
        'overdue_60': [],   # متأخرة 31-60 يوم
        'overdue_90': [],   # متأخرة 61-90 يوم
        'overdue_more': []  # متأخرة أكثر من 90 يوم
    }
    
    total_outstanding = Decimal('0')
    
    for invoice in outstanding:
        total_outstanding += invoice.total_amount
        
        if invoice.due_date >= current_date:
            categorized_invoices['current'].append(invoice)
        else:
            days_overdue = (current_date - invoice.due_date).days
            if days_overdue <= 30:
                categorized_invoices['overdue_30'].append(invoice)
            elif days_overdue <= 60:
                categorized_invoices['overdue_60'].append(invoice)
            elif days_overdue <= 90:
                categorized_invoices['overdue_90'].append(invoice)
            else:
                categorized_invoices['overdue_more'].append(invoice)
    
    # حساب المجاميع لكل فئة
    category_totals = {}
    for category, invoices in categorized_invoices.items():
        category_totals[category] = sum(inv.total_amount for inv in invoices)
    
    report_data = {
        'categorized_invoices': categorized_invoices,
        'category_totals': category_totals,
        'total_outstanding': total_outstanding
    }
    
    return render_template('reports/outstanding_invoices.html', **report_data)

@reports_bp.route('/tax-report')
@login_required
def tax_report():
    """التقرير الضريبي"""
    
    # معاملات التصفية
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    
    # التواريخ الافتراضية (الربع الحالي)
    if not date_from:
        current_date = datetime.now()
        quarter_start = datetime(current_date.year, ((current_date.month - 1) // 3) * 3 + 1, 1)
        date_from = quarter_start.strftime('%Y-%m-%d')
    if not date_to:
        date_to = datetime.now().strftime('%Y-%m-%d')
    
    try:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
    except ValueError:
        flash('التواريخ المدخلة غير صحيحة', 'error')
        return redirect(url_for('reports.reports_dashboard'))
    
    # الفواتير الخاضعة للضريبة
    taxable_invoices = Invoice.query.filter(
        Invoice.status == 'paid',
        Invoice.invoice_date >= date_from_obj,
        Invoice.invoice_date <= date_to_obj,
        Invoice.tax_amount > 0
    ).order_by(Invoice.invoice_date.desc()).all()
    
    # حساب إجمالي الضرائب
    total_tax_amount = sum(inv.tax_amount for inv in taxable_invoices)
    total_taxable_amount = sum(inv.subtotal - (inv.discount_amount or 0) for inv in taxable_invoices)
    
    report_data = {
        'date_from': date_from,
        'date_to': date_to,
        'taxable_invoices': taxable_invoices,
        'total_tax_amount': total_tax_amount,
        'total_taxable_amount': total_taxable_amount
    }
    
    return render_template('reports/tax_report.html', **report_data)
