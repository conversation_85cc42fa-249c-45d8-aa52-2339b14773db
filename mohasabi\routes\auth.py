"""
مسارات المصادقة والتسجيل
Authentication and registration routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from datetime import datetime

from models import db, User, UserActivity
from utils import get_client_ip, validate_email

# إنشاء Blueprint
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    
    # إذا كان المستخدم مسجل دخول بالفعل
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        remember_me = bool(request.form.get('remember_me'))
        
        # التحقق من صحة البيانات
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('auth/login.html')
        
        # البحث عن المستخدم
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()
        
        if user and user.check_password(password):
            if user.is_active:
                # تسجيل الدخول
                login_user(user, remember=remember_me)
                
                # تحديث آخر تسجيل دخول
                user.last_login = datetime.utcnow()
                
                # تسجيل النشاط
                activity = UserActivity(
                    user_id=user.id,
                    action='login',
                    description=f'تسجيل دخول من {get_client_ip()}',
                    ip_address=get_client_ip()
                )
                db.session.add(activity)
                db.session.commit()
                
                flash(f'مرحباً {user.full_name}!', 'success')
                
                # إعادة التوجيه للصفحة المطلوبة أو لوحة التحكم
                next_page = request.args.get('next')
                if next_page:
                    return redirect(next_page)
                return redirect(url_for('dashboard'))
            else:
                flash('حسابك معطل. يرجى الاتصال بالمدير.', 'error')
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    
    # تسجيل النشاط
    activity = UserActivity(
        user_id=current_user.id,
        action='logout',
        description=f'تسجيل خروج من {get_client_ip()}',
        ip_address=get_client_ip()
    )
    db.session.add(activity)
    db.session.commit()
    
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/profile')
@login_required
def profile():
    """الملف الشخصي"""
    
    # آخر الأنشطة
    recent_activities = UserActivity.query.filter_by(
        user_id=current_user.id
    ).order_by(UserActivity.created_at.desc()).limit(10).all()
    
    return render_template('auth/profile.html', 
                         user=current_user,
                         recent_activities=recent_activities)

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """تغيير كلمة المرور"""
    
    if request.method == 'POST':
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')
        
        # التحقق من كلمة المرور الحالية
        if not current_user.check_password(current_password):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return render_template('auth/change_password.html')
        
        # التحقق من كلمة المرور الجديدة
        if len(new_password) < 6:
            flash('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error')
            return render_template('auth/change_password.html')
        
        if new_password != confirm_password:
            flash('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'error')
            return render_template('auth/change_password.html')
        
        # تحديث كلمة المرور
        current_user.set_password(new_password)
        
        # تسجيل النشاط
        activity = UserActivity(
            user_id=current_user.id,
            action='password_change',
            description='تغيير كلمة المرور',
            ip_address=get_client_ip()
        )
        db.session.add(activity)
        db.session.commit()
        
        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('auth.profile'))
    
    return render_template('auth/change_password.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
@login_required
def register():
    """تسجيل مستخدم جديد (للمديرين فقط)"""
    
    # التحقق من صلاحية المدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإضافة مستخدمين جدد', 'error')
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        full_name = request.form.get('full_name', '').strip()
        password = request.form.get('password', '')
        role = request.form.get('role', 'viewer')
        
        # التحقق من صحة البيانات
        errors = []
        
        if not username or len(username) < 3:
            errors.append('اسم المستخدم يجب أن يكون 3 أحرف على الأقل')
        
        if User.query.filter_by(username=username).first():
            errors.append('اسم المستخدم موجود بالفعل')
        
        if not email or not validate_email(email):
            errors.append('البريد الإلكتروني غير صحيح')
        
        if User.query.filter_by(email=email).first():
            errors.append('البريد الإلكتروني موجود بالفعل')
        
        if not full_name or len(full_name) < 2:
            errors.append('الاسم الكامل مطلوب')
        
        if not password or len(password) < 6:
            errors.append('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
        
        if role not in ['admin', 'accountant', 'cashier', 'viewer']:
            errors.append('الدور المحدد غير صحيح')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('auth/register.html')
        
        # إنشاء المستخدم الجديد
        user = User(
            username=username,
            email=email,
            full_name=full_name,
            role=role
        )
        user.set_password(password)
        
        db.session.add(user)
        
        # تسجيل النشاط
        activity = UserActivity(
            user_id=current_user.id,
            action='user_create',
            description=f'إنشاء مستخدم جديد: {username}',
            ip_address=get_client_ip()
        )
        db.session.add(activity)
        db.session.commit()
        
        flash(f'تم إنشاء المستخدم {full_name} بنجاح', 'success')
        return redirect(url_for('settings.users'))
    
    return render_template('auth/register.html')

@auth_bp.route('/users')
@login_required
def users():
    """قائمة المستخدمين (للمديرين فقط)"""
    
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لعرض المستخدمين', 'error')
        return redirect(url_for('dashboard'))
    
    users = User.query.order_by(User.created_at.desc()).all()
    return render_template('auth/users.html', users=users)

@auth_bp.route('/users/<user_id>/toggle-status', methods=['POST'])
@login_required
def toggle_user_status(user_id):
    """تفعيل/تعطيل المستخدم"""
    
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لتعديل المستخدمين', 'error')
        return redirect(url_for('dashboard'))
    
    user = User.query.get_or_404(user_id)
    
    # منع تعطيل المدير الحالي
    if user.id == current_user.id:
        flash('لا يمكنك تعطيل حسابك الخاص', 'error')
        return redirect(url_for('auth.users'))
    
    user.is_active = not user.is_active
    status = 'تفعيل' if user.is_active else 'تعطيل'
    
    # تسجيل النشاط
    activity = UserActivity(
        user_id=current_user.id,
        action='user_status_change',
        description=f'{status} المستخدم: {user.username}',
        ip_address=get_client_ip()
    )
    db.session.add(activity)
    db.session.commit()
    
    flash(f'تم {status} المستخدم {user.full_name}', 'success')
    return redirect(url_for('auth.users'))
