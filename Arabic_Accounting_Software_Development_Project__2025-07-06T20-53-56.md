[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[/] NAME:إنشاء ملف التطبيق الرئيسي (app.py) DESCRIPTION:إنشاء ملف app.py الذي يحتوي على إعداد Flask والمسارات الأساسية مع دعم كامل للغة العربية ونظام المصادقة
-[ ] NAME:إكمال نماذج قاعدة البيانات DESCRIPTION:مراجعة وإكمال نماذج قاعدة البيانات في models.py لتشمل جميع الكيانات المطلوبة مثل الموردين والمنتجات والتقارير
-[ ] NAME:إنشاء واجهات المستخدم الأساسية DESCRIPTION:إنشاء قوالب HTML للوحة التحكم وإدارة الفواتير والعملاء مع تصميم عربي احترافي يدعم RTL
-[ ] NAME:تطوير نظام إدارة الفواتير DESCRIPTION:إنشاء وحدة كاملة لإدارة الفواتير تشمل الإنشاء والتحرير والطباعة والإرسال مع حساب الضرائب
-[ ] NAME:تطوير نظام إدارة العملاء والموردين DESCRIPTION:إنشاء واجهات وعمليات إدارة العملاء والموردين مع تتبع المعاملات والأرصدة
-[ ] NAME:تطوير نظام المصروفات والإيرادات DESCRIPTION:إنشاء وحدة تتبع المصروفات والإيرادات مع التصنيفات وربطها بالمشاريع والعملاء
-[ ] NAME:تطوير نظام التقارير المالية DESCRIPTION:إنشاء تقارير الأرباح والخسائر والميزانية والتدفق النقدي والتقارير الضريبية مع إمكانية التصدير
-[ ] NAME:تطوير نظام المستخدمين والصلاحيات DESCRIPTION:إنشاء نظام إدارة المستخدمين مع أدوار مختلفة (مدير، محاسب، أمين صندوق، مشاهد) وصلاحيات محددة
-[ ] NAME:تطوير نظام أرشفة المستندات DESCRIPTION:إنشاء نظام رفع وحفظ المستندات المالية مع ربطها بالمعاملات ونظام بحث متقدم
-[ ] NAME:تطوير نظام النسخ الاحتياطي DESCRIPTION:إنشاء نظام نسخ احتياطية تلقائية واستعادة البيانات مع تشفير البيانات الحساسة
-[ ] NAME:إعداد دعم العملات المتعددة DESCRIPTION:تطوير نظام دعم العملات المحلية (ريال سعودي، درهم إماراتي، جنيه مصري) مع أسعار الصرف
-[ ] NAME:تطوير واجهات API للتكامل المستقبلي DESCRIPTION:إنشاء واجهات برمجة تطبيقات RESTful للتكامل مع التطبيقات الخارجية والنسخة السحابية المستقبلية
-[ ] NAME:إعداد نظام التعبئة والتوزيع DESCRIPTION:إعداد PyInstaller لتحويل التطبيق إلى ملف exe قابل للتشغيل على Windows مع جميع المتطلبات
-[ ] NAME:اختبار شامل للنظام DESCRIPTION:إجراء اختبارات شاملة لجميع وظائف النظام وإصلاح الأخطاء وتحسين الأداء
-[ ] NAME:إنشاء دليل المستخدم والتوثيق DESCRIPTION:إنشاء دليل مستخدم شامل باللغة العربية مع لقطات شاشة وشرح مفصل لجميع الميزات