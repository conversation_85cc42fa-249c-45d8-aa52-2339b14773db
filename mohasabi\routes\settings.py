"""
مسارات الإعدادات والإدارة
Settings and administration routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from decimal import Decimal
import os
import shutil

from models import db, User, Category, PaymentMethod, UserActivity
from utils import create_backup_filename, get_client_ip

# إنشاء Blueprint
settings_bp = Blueprint('settings', __name__)

@settings_bp.route('/')
@login_required
def settings_dashboard():
    """لوحة تحكم الإعدادات"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ['admin']:
        flash('ليس لديك صلاحية للوصول إلى الإعدادات', 'error')
        return redirect(url_for('dashboard'))
    
    return render_template('settings/dashboard.html')

@settings_bp.route('/users')
@login_required
def users():
    """إدارة المستخدمين"""
    
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإدارة المستخدمين', 'error')
        return redirect(url_for('dashboard'))
    
    users = User.query.order_by(User.created_at.desc()).all()
    
    # إحصائيات المستخدمين
    stats = {
        'total_users': len(users),
        'active_users': len([u for u in users if u.is_active]),
        'admin_users': len([u for u in users if u.role == 'admin']),
        'recent_logins': len([u for u in users if u.last_login and 
                            (datetime.utcnow() - u.last_login).days <= 7])
    }
    
    return render_template('settings/users.html', users=users, stats=stats)

@settings_bp.route('/categories')
@login_required
def categories():
    """إدارة فئات المعاملات"""
    
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لإدارة الفئات', 'error')
        return redirect(url_for('dashboard'))
    
    # فئات الإيرادات
    income_categories = Category.query.filter_by(type='income', is_active=True).all()
    
    # فئات المصروفات
    expense_categories = Category.query.filter_by(type='expense', is_active=True).all()
    
    return render_template('settings/categories.html',
                         income_categories=income_categories,
                         expense_categories=expense_categories)

@settings_bp.route('/categories/new', methods=['GET', 'POST'])
@login_required
def new_category():
    """إضافة فئة جديدة"""
    
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لإضافة فئات جديدة', 'error')
        return redirect(url_for('settings.categories'))
    
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        type = request.form.get('type', '')
        parent_id = request.form.get('parent_id', '')
        
        # التحقق من صحة البيانات
        errors = []
        
        if not name or len(name) < 2:
            errors.append('اسم الفئة مطلوب ويجب أن يكون حرفين على الأقل')
        
        if type not in ['income', 'expense']:
            errors.append('نوع الفئة غير صحيح')
        
        # التحقق من عدم تكرار الاسم
        existing_category = Category.query.filter_by(name=name, type=type).first()
        if existing_category:
            errors.append('يوجد فئة بنفس الاسم والنوع')
        
        # التحقق من الفئة الأب
        parent = None
        if parent_id:
            parent = Category.query.get(parent_id)
            if not parent or parent.type != type:
                errors.append('الفئة الأب غير صحيحة')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('settings/new_category.html')
        
        # إنشاء الفئة الجديدة
        category = Category(
            name=name,
            type=type,
            parent_id=parent_id if parent_id else None
        )
        
        db.session.add(category)
        db.session.commit()
        
        flash(f'تم إضافة الفئة {name} بنجاح', 'success')
        return redirect(url_for('settings.categories'))
    
    # قائمة الفئات الأب المحتملة
    parent_categories = Category.query.filter_by(parent_id=None, is_active=True).all()
    
    return render_template('settings/new_category.html', parent_categories=parent_categories)

@settings_bp.route('/payment-methods')
@login_required
def payment_methods():
    """إدارة طرق الدفع"""
    
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لإدارة طرق الدفع', 'error')
        return redirect(url_for('dashboard'))
    
    methods = PaymentMethod.query.filter_by(is_active=True).order_by(PaymentMethod.name).all()
    
    return render_template('settings/payment_methods.html', methods=methods)

@settings_bp.route('/payment-methods/new', methods=['GET', 'POST'])
@login_required
def new_payment_method():
    """إضافة طريقة دفع جديدة"""
    
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لإضافة طرق دفع جديدة', 'error')
        return redirect(url_for('settings.payment_methods'))
    
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        type = request.form.get('type', '')
        description = request.form.get('description', '').strip()
        
        # التحقق من صحة البيانات
        errors = []
        
        if not name or len(name) < 2:
            errors.append('اسم طريقة الدفع مطلوب')
        
        if type not in ['cash', 'bank', 'card', 'online']:
            errors.append('نوع طريقة الدفع غير صحيح')
        
        # التحقق من عدم تكرار الاسم
        existing_method = PaymentMethod.query.filter_by(name=name).first()
        if existing_method:
            errors.append('يوجد طريقة دفع بنفس الاسم')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('settings/new_payment_method.html')
        
        # إنشاء طريقة الدفع الجديدة
        method = PaymentMethod(
            name=name,
            type=type,
            description=description if description else None
        )
        
        db.session.add(method)
        db.session.commit()
        
        flash(f'تم إضافة طريقة الدفع {name} بنجاح', 'success')
        return redirect(url_for('settings.payment_methods'))
    
    return render_template('settings/new_payment_method.html')

@settings_bp.route('/backup')
@login_required
def backup():
    """إدارة النسخ الاحتياطية"""
    
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإدارة النسخ الاحتياطية', 'error')
        return redirect(url_for('dashboard'))
    
    # قائمة النسخ الاحتياطية الموجودة
    backup_folder = 'backups'
    backups = []
    
    if os.path.exists(backup_folder):
        for filename in os.listdir(backup_folder):
            if filename.endswith('.db'):
                file_path = os.path.join(backup_folder, filename)
                file_stats = os.stat(file_path)
                backups.append({
                    'filename': filename,
                    'size': file_stats.st_size,
                    'created_at': datetime.fromtimestamp(file_stats.st_ctime)
                })
    
    # ترتيب حسب تاريخ الإنشاء
    backups.sort(key=lambda x: x['created_at'], reverse=True)
    
    return render_template('settings/backup.html', backups=backups)

@settings_bp.route('/backup/create', methods=['POST'])
@login_required
def create_backup():
    """إنشاء نسخة احتياطية"""
    
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإنشاء نسخ احتياطية', 'error')
        return redirect(url_for('settings.backup'))
    
    try:
        # إنشاء مجلد النسخ الاحتياطية
        backup_folder = 'backups'
        if not os.path.exists(backup_folder):
            os.makedirs(backup_folder)
        
        # نسخ قاعدة البيانات
        source_db = 'mohasabi.db'
        backup_filename = create_backup_filename()
        backup_path = os.path.join(backup_folder, backup_filename)
        
        if os.path.exists(source_db):
            shutil.copy2(source_db, backup_path)
            
            # تسجيل النشاط
            activity = UserActivity(
                user_id=current_user.id,
                action='backup_create',
                description=f'إنشاء نسخة احتياطية: {backup_filename}',
                ip_address=get_client_ip()
            )
            db.session.add(activity)
            db.session.commit()
            
            flash(f'تم إنشاء النسخة الاحتياطية {backup_filename} بنجاح', 'success')
        else:
            flash('ملف قاعدة البيانات غير موجود', 'error')
    
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'error')
    
    return redirect(url_for('settings.backup'))

@settings_bp.route('/system-info')
@login_required
def system_info():
    """معلومات النظام"""
    
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لعرض معلومات النظام', 'error')
        return redirect(url_for('dashboard'))
    
    # إحصائيات قاعدة البيانات
    from models import Customer, Invoice, Transaction
    
    stats = {
        'total_customers': Customer.query.count(),
        'active_customers': Customer.query.filter_by(is_active=True).count(),
        'total_invoices': Invoice.query.count(),
        'paid_invoices': Invoice.query.filter_by(status='paid').count(),
        'total_transactions': Transaction.query.count(),
        'database_size': os.path.getsize('mohasabi.db') if os.path.exists('mohasabi.db') else 0
    }
    
    # آخر الأنشطة
    recent_activities = UserActivity.query.order_by(
        UserActivity.created_at.desc()
    ).limit(20).all()
    
    return render_template('settings/system_info.html', 
                         stats=stats, 
                         recent_activities=recent_activities)

@settings_bp.route('/activity-log')
@login_required
def activity_log():
    """سجل الأنشطة"""
    
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لعرض سجل الأنشطة', 'error')
        return redirect(url_for('dashboard'))
    
    # معاملات التصفية
    user_id = request.args.get('user_id', '')
    action = request.args.get('action', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    page = request.args.get('page', 1, type=int)
    per_page = 50
    
    # بناء الاستعلام
    query = UserActivity.query
    
    if user_id:
        query = query.filter_by(user_id=user_id)
    
    if action:
        query = query.filter_by(action=action)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(UserActivity.created_at >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(UserActivity.created_at <= date_to_obj)
        except ValueError:
            pass
    
    # ترتيب وتصفح
    activities = query.order_by(UserActivity.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # قائمة المستخدمين للتصفية
    users = User.query.order_by(User.full_name).all()
    
    # قائمة الأنشطة المتاحة
    available_actions = db.session.query(UserActivity.action).distinct().all()
    actions = [action[0] for action in available_actions]
    
    return render_template('settings/activity_log.html',
                         activities=activities,
                         users=users,
                         actions=actions,
                         user_id=user_id,
                         action=action,
                         date_from=date_from,
                         date_to=date_to)
