"""
مسارات إدارة العملاء
Customer management routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from decimal import Decimal

from models import db, Customer, Invoice, Transaction
from utils import validate_email, format_phone_number, get_client_ip

# إنشاء Blueprint
customers_bp = Blueprint('customers', __name__)

@customers_bp.route('/')
@login_required
def list_customers():
    """قائمة العملاء"""
    
    # معاملات البحث والتصفية
    search = request.args.get('search', '').strip()
    status = request.args.get('status', 'all')
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # بناء الاستعلام
    query = Customer.query
    
    if search:
        query = query.filter(
            (Customer.name.contains(search)) |
            (Customer.email.contains(search)) |
            (Customer.phone.contains(search))
        )
    
    if status == 'active':
        query = query.filter_by(is_active=True)
    elif status == 'inactive':
        query = query.filter_by(is_active=False)
    
    # ترتيب وتصفح
    customers = query.order_by(Customer.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('customers/list.html', 
                         customers=customers,
                         search=search,
                         status=status)

@customers_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_customer():
    """إضافة عميل جديد"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لإضافة عملاء جدد', 'error')
        return redirect(url_for('customers.list_customers'))
    
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip()
        phone = request.form.get('phone', '').strip()
        address = request.form.get('address', '').strip()
        tax_number = request.form.get('tax_number', '').strip()
        credit_limit = request.form.get('credit_limit', '0')
        notes = request.form.get('notes', '').strip()
        
        # التحقق من صحة البيانات
        errors = []
        
        if not name or len(name) < 2:
            errors.append('اسم العميل مطلوب ويجب أن يكون حرفين على الأقل')
        
        # التحقق من عدم تكرار الاسم
        if Customer.query.filter_by(name=name).first():
            errors.append('يوجد عميل بنفس الاسم بالفعل')
        
        if email and not validate_email(email):
            errors.append('البريد الإلكتروني غير صحيح')
        
        # التحقق من عدم تكرار البريد الإلكتروني
        if email and Customer.query.filter_by(email=email).first():
            errors.append('البريد الإلكتروني مستخدم بالفعل')
        
        try:
            credit_limit = Decimal(credit_limit) if credit_limit else Decimal('0')
            if credit_limit < 0:
                errors.append('الحد الائتماني لا يمكن أن يكون سالباً')
        except:
            errors.append('الحد الائتماني يجب أن يكون رقماً صحيحاً')
            credit_limit = Decimal('0')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('customers/new.html')
        
        # إنشاء العميل الجديد
        customer = Customer(
            name=name,
            email=email if email else None,
            phone=format_phone_number(phone) if phone else None,
            address=address if address else None,
            tax_number=tax_number if tax_number else None,
            credit_limit=credit_limit,
            notes=notes if notes else None
        )
        
        db.session.add(customer)
        db.session.commit()
        
        flash(f'تم إضافة العميل {name} بنجاح', 'success')
        return redirect(url_for('customers.view_customer', customer_id=customer.id))
    
    return render_template('customers/new.html')

@customers_bp.route('/<customer_id>')
@login_required
def view_customer(customer_id):
    """عرض تفاصيل العميل"""
    
    customer = Customer.query.get_or_404(customer_id)
    
    # إحصائيات العميل
    stats = {
        'total_invoices': customer.invoices.count(),
        'paid_invoices': customer.invoices.filter_by(status='paid').count(),
        'pending_invoices': customer.invoices.filter_by(status='sent').count(),
        'overdue_invoices': customer.invoices.filter(
            Invoice.status == 'sent',
            Invoice.due_date < datetime.now().date()
        ).count(),
        'total_sales': customer.get_total_sales(),
        'outstanding_balance': customer.get_outstanding_balance(),
        'average_invoice': customer.get_average_invoice_amount()
    }
    
    # آخر الفواتير
    recent_invoices = customer.invoices.order_by(
        Invoice.created_at.desc()
    ).limit(10).all()
    
    # آخر المعاملات
    recent_transactions = customer.transactions.order_by(
        Transaction.transaction_date.desc()
    ).limit(10).all()
    
    return render_template('customers/view.html',
                         customer=customer,
                         stats=stats,
                         recent_invoices=recent_invoices,
                         recent_transactions=recent_transactions)

@customers_bp.route('/<customer_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_customer(customer_id):
    """تحرير بيانات العميل"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لتحرير بيانات العملاء', 'error')
        return redirect(url_for('customers.view_customer', customer_id=customer_id))
    
    customer = Customer.query.get_or_404(customer_id)
    
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip()
        phone = request.form.get('phone', '').strip()
        address = request.form.get('address', '').strip()
        tax_number = request.form.get('tax_number', '').strip()
        credit_limit = request.form.get('credit_limit', '0')
        notes = request.form.get('notes', '').strip()
        
        # التحقق من صحة البيانات
        errors = []
        
        if not name or len(name) < 2:
            errors.append('اسم العميل مطلوب ويجب أن يكون حرفين على الأقل')
        
        # التحقق من عدم تكرار الاسم (باستثناء العميل الحالي)
        existing_customer = Customer.query.filter(
            Customer.name == name,
            Customer.id != customer.id
        ).first()
        if existing_customer:
            errors.append('يوجد عميل آخر بنفس الاسم')
        
        if email and not validate_email(email):
            errors.append('البريد الإلكتروني غير صحيح')
        
        # التحقق من عدم تكرار البريد الإلكتروني (باستثناء العميل الحالي)
        if email:
            existing_email = Customer.query.filter(
                Customer.email == email,
                Customer.id != customer.id
            ).first()
            if existing_email:
                errors.append('البريد الإلكتروني مستخدم بالفعل')
        
        try:
            credit_limit = Decimal(credit_limit) if credit_limit else Decimal('0')
            if credit_limit < 0:
                errors.append('الحد الائتماني لا يمكن أن يكون سالباً')
        except:
            errors.append('الحد الائتماني يجب أن يكون رقماً صحيحاً')
            credit_limit = customer.credit_limit
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('customers/edit.html', customer=customer)
        
        # تحديث بيانات العميل
        customer.name = name
        customer.email = email if email else None
        customer.phone = format_phone_number(phone) if phone else None
        customer.address = address if address else None
        customer.tax_number = tax_number if tax_number else None
        customer.credit_limit = credit_limit
        customer.notes = notes if notes else None
        customer.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        flash(f'تم تحديث بيانات العميل {name} بنجاح', 'success')
        return redirect(url_for('customers.view_customer', customer_id=customer.id))
    
    return render_template('customers/edit.html', customer=customer)

@customers_bp.route('/<customer_id>/toggle-status', methods=['POST'])
@login_required
def toggle_customer_status(customer_id):
    """تفعيل/تعطيل العميل"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لتغيير حالة العملاء', 'error')
        return redirect(url_for('customers.view_customer', customer_id=customer_id))
    
    customer = Customer.query.get_or_404(customer_id)
    
    customer.is_active = not customer.is_active
    customer.updated_at = datetime.utcnow()
    
    db.session.commit()
    
    status = 'تفعيل' if customer.is_active else 'تعطيل'
    flash(f'تم {status} العميل {customer.name}', 'success')
    
    return redirect(url_for('customers.view_customer', customer_id=customer.id))

@customers_bp.route('/api/search')
@login_required
def api_search_customers():
    """البحث عن العملاء (API)"""
    
    query = request.args.get('q', '').strip()
    limit = request.args.get('limit', 10, type=int)
    
    if not query:
        return jsonify([])
    
    customers = Customer.query.filter(
        Customer.is_active == True,
        (Customer.name.contains(query)) |
        (Customer.email.contains(query)) |
        (Customer.phone.contains(query))
    ).limit(limit).all()
    
    results = []
    for customer in customers:
        results.append({
            'id': customer.id,
            'name': customer.name,
            'email': customer.email,
            'phone': customer.phone,
            'outstanding_balance': float(customer.get_outstanding_balance())
        })
    
    return jsonify(results)
