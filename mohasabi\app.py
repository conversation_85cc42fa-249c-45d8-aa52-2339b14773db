#!/usr/bin/env python3
"""
التطبيق الرئيسي لنظام محاسبي
Main application for Mohasabi accounting system
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from datetime import datetime, timedelta
import os
import logging
from logging.handlers import RotatingFileHandler

# استيراد الوحدات المحلية
from config import config
from models import db, User, Customer, Invoice, Transaction, Category, PaymentMethod, init_default_data
from utils import format_currency, format_date_arabic, validate_email, get_client_ip

# إنشاء مدير تسجيل الدخول
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    """تحميل المستخدم"""
    return User.query.get(user_id)

def create_app(config_name=None):
    """إنشاء تطبيق Flask"""
    
    # إنشاء التطبيق
    app = Flask(__name__)
    
    # تحديد بيئة التشغيل
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # تطبيق الإعدادات
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # تهيئة الإضافات
    db.init_app(app)
    login_manager.init_app(app)
    
    # تسجيل المسارات (Blueprints)
    register_blueprints(app)
    
    # تسجيل معالجات الأخطاء
    register_error_handlers(app)
    
    # تسجيل المرشحات المخصصة
    register_template_filters(app)
    
    # إعداد السجلات
    setup_logging(app)
    
    # إنشاء قاعدة البيانات والبيانات الافتراضية
    with app.app_context():
        db.create_all()
        init_default_data()
    
    return app

def register_blueprints(app):
    """تسجيل المسارات"""
    
    # المسار الرئيسي
    @app.route('/')
    def index():
        """الصفحة الرئيسية"""
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('auth.login'))
    
    # لوحة التحكم
    @app.route('/dashboard')
    @login_required
    def dashboard():
        """لوحة التحكم الرئيسية"""
        
        # إحصائيات سريعة
        stats = {
            'total_customers': Customer.query.filter_by(is_active=True).count(),
            'total_invoices': Invoice.query.count(),
            'pending_invoices': Invoice.query.filter_by(status='sent').count(),
            'overdue_invoices': Invoice.query.filter(
                Invoice.status == 'sent',
                Invoice.due_date < datetime.now().date()
            ).count(),
            'total_revenue': db.session.query(db.func.sum(Invoice.total_amount)).filter(
                Invoice.status == 'paid'
            ).scalar() or 0,
            'pending_amount': db.session.query(db.func.sum(Invoice.total_amount)).filter(
                Invoice.status.in_(['sent', 'overdue'])
            ).scalar() or 0
        }
        
        # الفواتير الحديثة
        recent_invoices = Invoice.query.order_by(Invoice.created_at.desc()).limit(5).all()
        
        # العملاء الجدد
        recent_customers = Customer.query.order_by(Customer.created_at.desc()).limit(5).all()
        
        return render_template('dashboard.html',
                             stats=stats,
                             recent_invoices=recent_invoices,
                             recent_customers=recent_customers)
    
    # تسجيل مسارات المصادقة
    from routes.auth import auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    # تسجيل مسارات الفواتير
    from routes.invoices import invoices_bp
    app.register_blueprint(invoices_bp, url_prefix='/invoices')
    
    # تسجيل مسارات العملاء
    from routes.customers import customers_bp
    app.register_blueprint(customers_bp, url_prefix='/customers')
    
    # تسجيل مسارات التقارير
    from routes.reports import reports_bp
    app.register_blueprint(reports_bp, url_prefix='/reports')
    
    # تسجيل مسارات الإعدادات
    from routes.settings import settings_bp
    app.register_blueprint(settings_bp, url_prefix='/settings')

def register_error_handlers(app):
    """تسجيل معالجات الأخطاء"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        """صفحة غير موجودة"""
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """خطأ داخلي في الخادم"""
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        """ممنوع الوصول"""
        return render_template('errors/403.html'), 403

def register_template_filters(app):
    """تسجيل المرشحات المخصصة للقوالب"""
    
    @app.template_filter('currency')
    def currency_filter(amount, currency='SAR'):
        """تنسيق العملة"""
        return format_currency(amount, currency)
    
    @app.template_filter('arabic_date')
    def arabic_date_filter(date):
        """تنسيق التاريخ بالعربية"""
        return format_date_arabic(date)
    
    @app.template_filter('status_badge')
    def status_badge_filter(status):
        """شارة الحالة"""
        status_map = {
            'draft': {'class': 'secondary', 'text': 'مسودة'},
            'sent': {'class': 'primary', 'text': 'مرسلة'},
            'paid': {'class': 'success', 'text': 'مدفوعة'},
            'overdue': {'class': 'danger', 'text': 'متأخرة'},
            'cancelled': {'class': 'dark', 'text': 'ملغية'}
        }
        
        badge_info = status_map.get(status, {'class': 'secondary', 'text': status})
        return f'<span class="badge bg-{badge_info["class"]}">{badge_info["text"]}</span>'

def setup_logging(app):
    """إعداد نظام السجلات"""
    
    if not app.debug and not app.testing:
        # إنشاء مجلد السجلات
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        # إعداد ملف السجل
        file_handler = RotatingFileHandler(
            'logs/mohasabi.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('تم بدء تشغيل نظام محاسبي')

# متغيرات السياق العامة
@login_manager.user_loader
def load_user(user_id):
    """تحميل المستخدم"""
    return User.query.get(user_id)

# دوال مساعدة للقوالب
def inject_template_vars():
    """حقن متغيرات في جميع القوالب"""
    return {
        'app_name': 'محاسبي',
        'app_version': '1.0.0',
        'current_year': datetime.now().year,
        'currencies': ['SAR', 'AED', 'EGP', 'USD', 'EUR']
    }

if __name__ == '__main__':
    # تشغيل التطبيق مباشرة (للتطوير فقط)
    app = create_app('development')
    app.run(host='0.0.0.0', port=5000, debug=True)
