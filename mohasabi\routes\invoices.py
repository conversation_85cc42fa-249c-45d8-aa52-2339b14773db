"""
مسارات إدارة الفواتير
Invoice management routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, make_response
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from decimal import Decimal
import json

from models import db, Invoice, InvoiceItem, Customer, Payment
from utils import generate_invoice_number, calculate_due_date, format_currency

# إنشاء Blueprint
invoices_bp = Blueprint('invoices', __name__)

@invoices_bp.route('/')
@login_required
def list_invoices():
    """قائمة الفواتير"""
    
    # معاملات البحث والتصفية
    search = request.args.get('search', '').strip()
    status = request.args.get('status', 'all')
    customer_id = request.args.get('customer_id', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # بناء الاستعلام
    query = Invoice.query
    
    if search:
        query = query.join(Customer).filter(
            (Invoice.invoice_number.contains(search)) |
            (Customer.name.contains(search))
        )
    
    if status != 'all':
        query = query.filter_by(status=status)
    
    if customer_id:
        query = query.filter_by(customer_id=customer_id)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(Invoice.invoice_date >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(Invoice.invoice_date <= date_to_obj)
        except ValueError:
            pass
    
    # ترتيب وتصفح
    invoices = query.order_by(Invoice.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # قائمة العملاء للتصفية
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
    
    return render_template('invoices/list.html',
                         invoices=invoices,
                         customers=customers,
                         search=search,
                         status=status,
                         customer_id=customer_id,
                         date_from=date_from,
                         date_to=date_to)

@invoices_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_invoice():
    """إنشاء فاتورة جديدة"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لإنشاء فواتير جديدة', 'error')
        return redirect(url_for('invoices.list_invoices'))
    
    if request.method == 'POST':
        customer_id = request.form.get('customer_id')
        invoice_date = request.form.get('invoice_date')
        due_date = request.form.get('due_date')
        currency = request.form.get('currency', 'SAR')
        tax_rate = request.form.get('tax_rate', '15')
        discount_amount = request.form.get('discount_amount', '0')
        notes = request.form.get('notes', '').strip()
        
        # بيانات العناصر
        items_data = request.form.get('items_data', '[]')
        
        # التحقق من صحة البيانات
        errors = []
        
        if not customer_id:
            errors.append('يجب اختيار العميل')
        
        customer = Customer.query.get(customer_id) if customer_id else None
        if not customer:
            errors.append('العميل المحدد غير موجود')
        
        try:
            invoice_date_obj = datetime.strptime(invoice_date, '%Y-%m-%d').date()
        except:
            errors.append('تاريخ الفاتورة غير صحيح')
            invoice_date_obj = datetime.now().date()
        
        try:
            due_date_obj = datetime.strptime(due_date, '%Y-%m-%d').date()
        except:
            due_date_obj = calculate_due_date(invoice_date_obj)
        
        try:
            tax_rate = Decimal(tax_rate)
            if tax_rate < 0 or tax_rate > 100:
                errors.append('معدل الضريبة يجب أن يكون بين 0 و 100')
        except:
            errors.append('معدل الضريبة غير صحيح')
            tax_rate = Decimal('15')
        
        try:
            discount_amount = Decimal(discount_amount) if discount_amount else Decimal('0')
            if discount_amount < 0:
                errors.append('مبلغ الخصم لا يمكن أن يكون سالباً')
        except:
            errors.append('مبلغ الخصم غير صحيح')
            discount_amount = Decimal('0')
        
        # تحليل بيانات العناصر
        try:
            items = json.loads(items_data)
            if not items:
                errors.append('يجب إضافة عنصر واحد على الأقل')
        except:
            errors.append('بيانات العناصر غير صحيحة')
            items = []
        
        if errors:
            for error in errors:
                flash(error, 'error')
            customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
            return render_template('invoices/new.html', customers=customers)
        
        # إنشاء الفاتورة
        invoice = Invoice(
            invoice_number=generate_invoice_number(),
            customer_id=customer_id,
            invoice_date=invoice_date_obj,
            due_date=due_date_obj,
            currency=currency,
            tax_rate=tax_rate,
            discount_amount=discount_amount,
            notes=notes if notes else None,
            created_by=current_user.id
        )
        
        db.session.add(invoice)
        db.session.flush()  # للحصول على ID الفاتورة
        
        # إضافة العناصر
        subtotal = Decimal('0')
        for item_data in items:
            try:
                item = InvoiceItem(
                    invoice_id=invoice.id,
                    description=item_data['description'],
                    quantity=Decimal(str(item_data['quantity'])),
                    unit_price=Decimal(str(item_data['unit_price'])),
                    total_amount=Decimal(str(item_data['quantity'])) * Decimal(str(item_data['unit_price']))
                )
                db.session.add(item)
                subtotal += item.total_amount
            except:
                continue
        
        # حساب الإجماليات
        invoice.subtotal = subtotal
        invoice.tax_amount = (subtotal - discount_amount) * (tax_rate / 100)
        invoice.total_amount = subtotal + invoice.tax_amount - discount_amount
        
        db.session.commit()
        
        flash(f'تم إنشاء الفاتورة {invoice.invoice_number} بنجاح', 'success')
        return redirect(url_for('invoices.view_invoice', invoice_id=invoice.id))
    
    # عرض نموذج الإنشاء
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
    return render_template('invoices/new.html', customers=customers)

@invoices_bp.route('/<invoice_id>')
@login_required
def view_invoice(invoice_id):
    """عرض تفاصيل الفاتورة"""
    
    invoice = Invoice.query.get_or_404(invoice_id)
    
    # المدفوعات
    payments = invoice.payments.order_by(Payment.payment_date.desc()).all()
    
    return render_template('invoices/view.html',
                         invoice=invoice,
                         payments=payments)

@invoices_bp.route('/<invoice_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_invoice(invoice_id):
    """تحرير الفاتورة"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لتحرير الفواتير', 'error')
        return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))
    
    invoice = Invoice.query.get_or_404(invoice_id)
    
    # لا يمكن تحرير الفواتير المدفوعة
    if invoice.status == 'paid':
        flash('لا يمكن تحرير فاتورة مدفوعة', 'error')
        return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))
    
    if request.method == 'POST':
        # نفس منطق الإنشاء مع التحديث
        # ... (سيتم إكمالها في الجزء التالي)
        pass
    
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
    return render_template('invoices/edit.html', invoice=invoice, customers=customers)

@invoices_bp.route('/<invoice_id>/send', methods=['POST'])
@login_required
def send_invoice(invoice_id):
    """إرسال الفاتورة"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لإرسال الفواتير', 'error')
        return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))
    
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if invoice.status != 'draft':
        flash('يمكن إرسال المسودات فقط', 'error')
        return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))
    
    invoice.status = 'sent'
    invoice.sent_at = datetime.utcnow()
    
    db.session.commit()
    
    flash(f'تم إرسال الفاتورة {invoice.invoice_number}', 'success')
    return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))

@invoices_bp.route('/<invoice_id>/cancel', methods=['POST'])
@login_required
def cancel_invoice(invoice_id):
    """إلغاء الفاتورة"""
    
    # التحقق من الصلاحيات
    if current_user.role not in ['admin', 'accountant']:
        flash('ليس لديك صلاحية لإلغاء الفواتير', 'error')
        return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))
    
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if invoice.status == 'paid':
        flash('لا يمكن إلغاء فاتورة مدفوعة', 'error')
        return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))
    
    invoice.status = 'cancelled'
    
    db.session.commit()
    
    flash(f'تم إلغاء الفاتورة {invoice.invoice_number}', 'success')
    return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))

@invoices_bp.route('/<invoice_id>/print')
@login_required
def print_invoice(invoice_id):
    """طباعة الفاتورة"""
    
    invoice = Invoice.query.get_or_404(invoice_id)
    
    return render_template('invoices/print.html', invoice=invoice)

@invoices_bp.route('/<invoice_id>/pdf')
@login_required
def download_pdf(invoice_id):
    """تحميل الفاتورة كـ PDF"""
    
    invoice = Invoice.query.get_or_404(invoice_id)
    
    # سيتم تطوير هذه الوظيفة لاحقاً باستخدام ReportLab
    flash('ميزة تحميل PDF قيد التطوير', 'info')
    return redirect(url_for('invoices.view_invoice', invoice_id=invoice_id))
