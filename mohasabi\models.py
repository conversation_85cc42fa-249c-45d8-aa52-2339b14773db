"""
نماذج قاعدة البيانات لنظام محاسبي
Database models for Mohasabi accounting system
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
from decimal import Decimal
import uuid
from sqlalchemy import event
from sqlalchemy.dialects.sqlite import JSON

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='viewer')  # admin, accountant, cashier, viewer
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    created_invoices = db.relationship('Invoice', foreign_keys='Invoice.created_by', backref='creator', lazy='dynamic')
    user_activities = db.relationship('UserActivity', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def set_password(self, password):
        """تشفير كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    def get_role_display(self):
        """عرض الدور بالعربية"""
        roles = {
            'admin': 'مدير',
            'accountant': 'محاسب',
            'cashier': 'أمين صندوق',
            'viewer': 'مشاهد'
        }
        return roles.get(self.role, self.role)
    
    def can_access(self, resource):
        """التحقق من صلاحية الوصول"""
        permissions = {
            'admin': ['all'],
            'accountant': ['invoices', 'customers', 'reports', 'transactions'],
            'cashier': ['transactions', 'payments'],
            'viewer': ['reports']
        }
        
        user_permissions = permissions.get(self.role, [])
        return 'all' in user_permissions or resource in user_permissions
    
    def __repr__(self):
        return f'<User {self.username}>'

class Customer(db.Model):
    """نموذج العملاء"""
    __tablename__ = 'customers'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False, index=True)
    email = db.Column(db.String(120), index=True)
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_number = db.Column(db.String(50))  # الرقم الضريبي
    credit_limit = db.Column(db.Numeric(15, 2), default=0)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='customer', lazy='dynamic', cascade='all, delete-orphan')
    transactions = db.relationship('Transaction', backref='customer', lazy='dynamic')
    
    def get_total_sales(self):
        """إجمالي المبيعات"""
        return sum(invoice.total_amount for invoice in self.invoices if invoice.status != 'cancelled')
    
    def get_outstanding_balance(self):
        """الرصيد المستحق"""
        return sum(invoice.total_amount for invoice in self.invoices if invoice.status in ['sent', 'overdue'])
    
    def get_average_invoice_amount(self):
        """متوسط قيمة الفاتورة"""
        invoices = [inv for inv in self.invoices if inv.status != 'cancelled']
        if not invoices:
            return Decimal('0.00')
        return sum(inv.total_amount for inv in invoices) / len(invoices)
    
    def get_last_invoice_date(self):
        """تاريخ آخر فاتورة"""
        last_invoice = self.invoices.order_by(Invoice.invoice_date.desc()).first()
        return last_invoice.invoice_date if last_invoice else None
    
    def get_payment_history(self):
        """تاريخ المدفوعات"""
        payments = []
        for invoice in self.invoices:
            payments.extend(invoice.payments)
        return sorted(payments, key=lambda x: x.payment_date, reverse=True)
    
    def __repr__(self):
        return f'<Customer {self.name}>'

class Supplier(db.Model):
    """نموذج الموردين"""
    __tablename__ = 'suppliers'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False, index=True)
    email = db.Column(db.String(120), index=True)
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_number = db.Column(db.String(50))
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    transactions = db.relationship('Transaction', backref='supplier', lazy='dynamic')
    
    def __repr__(self):
        return f'<Supplier {self.name}>'

class Invoice(db.Model):
    """نموذج الفواتير"""
    __tablename__ = 'invoices'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    invoice_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    customer_id = db.Column(db.String(36), db.ForeignKey('customers.id'))
    invoice_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    due_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='draft', nullable=False)  # draft, sent, paid, overdue, cancelled
    currency = db.Column(db.String(3), default='SAR', nullable=False)
    exchange_rate = db.Column(db.Numeric(10, 4), default=1.0000)
    
    # المبالغ
    subtotal = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    tax_rate = db.Column(db.Numeric(5, 2), default=15.00, nullable=False)  # نسبة الضريبة
    tax_amount = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    discount_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    
    # معلومات إضافية
    notes = db.Column(db.Text)
    sent_at = db.Column(db.DateTime)
    created_by = db.Column(db.String(36), db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    payments = db.relationship('Payment', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    
    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        self.subtotal = sum(item.total_amount for item in self.items)
        self.tax_amount = (self.subtotal - (self.discount_amount or 0)) * (self.tax_rate / 100)
        self.total_amount = self.subtotal + self.tax_amount - (self.discount_amount or 0)
    
    def get_paid_amount(self):
        """المبلغ المدفوع"""
        return sum(payment.amount for payment in self.payments)
    
    def get_remaining_amount(self):
        """المبلغ المتبقي"""
        return self.total_amount - self.get_paid_amount()
    
    def is_overdue(self):
        """هل الفاتورة متأخرة"""
        if self.status == 'sent' and self.due_date:
            return self.due_date < datetime.now().date()
        return False
    
    def get_status_display(self):
        """عرض الحالة بالعربية"""
        statuses = {
            'draft': 'مسودة',
            'sent': 'مرسلة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغية'
        }
        return statuses.get(self.status, self.status)
    
    def update_status(self):
        """تحديث حالة الفاتورة تلقائياً"""
        if self.status == 'sent':
            if self.get_paid_amount() >= self.total_amount:
                self.status = 'paid'
            elif self.is_overdue():
                self.status = 'overdue'
    
    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

class InvoiceItem(db.Model):
    """عناصر الفاتورة"""
    __tablename__ = 'invoice_items'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    invoice_id = db.Column(db.String(36), db.ForeignKey('invoices.id'), nullable=False)
    description = db.Column(db.String(255), nullable=False)
    quantity = db.Column(db.Numeric(10, 3), nullable=False, default=1)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def calculate_total(self):
        """حساب إجمالي العنصر"""
        self.total_amount = self.quantity * self.unit_price
    
    def __repr__(self):
        return f'<InvoiceItem {self.description}>'

class Payment(db.Model):
    """المدفوعات"""
    __tablename__ = 'payments'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    invoice_id = db.Column(db.String(36), db.ForeignKey('invoices.id'))
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    payment_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    payment_method_id = db.Column(db.String(36), db.ForeignKey('payment_methods.id'))
    reference_number = db.Column(db.String(100))  # رقم المرجع
    notes = db.Column(db.Text)
    created_by = db.Column(db.String(36), db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f'<Payment {self.amount}>'

class Transaction(db.Model):
    """المعاملات المالية"""
    __tablename__ = 'transactions'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    type = db.Column(db.String(20), nullable=False)  # income, expense
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    description = db.Column(db.String(255), nullable=False)
    transaction_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())

    # العلاقات
    category_id = db.Column(db.String(36), db.ForeignKey('categories.id'))
    customer_id = db.Column(db.String(36), db.ForeignKey('customers.id'))
    supplier_id = db.Column(db.String(36), db.ForeignKey('suppliers.id'))
    payment_method_id = db.Column(db.String(36), db.ForeignKey('payment_methods.id'))

    # معلومات إضافية
    reference_number = db.Column(db.String(100))
    notes = db.Column(db.Text)
    created_by = db.Column(db.String(36), db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def get_type_display(self):
        """عرض نوع المعاملة بالعربية"""
        types = {
            'income': 'إيراد',
            'expense': 'مصروف'
        }
        return types.get(self.type, self.type)

    def __repr__(self):
        return f'<Transaction {self.description}>'

class Category(db.Model):
    """فئات المعاملات"""
    __tablename__ = 'categories'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(20), nullable=False)  # income, expense
    parent_id = db.Column(db.String(36), db.ForeignKey('categories.id'))
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # العلاقات
    transactions = db.relationship('Transaction', backref='category', lazy='dynamic')
    children = db.relationship('Category', backref=db.backref('parent', remote_side=[id]), lazy='dynamic')

    def __repr__(self):
        return f'<Category {self.name}>'

class PaymentMethod(db.Model):
    """طرق الدفع"""
    __tablename__ = 'payment_methods'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(20), nullable=False)  # cash, bank, card, online
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # العلاقات
    payments = db.relationship('Payment', backref='payment_method', lazy='dynamic')
    transactions = db.relationship('Transaction', backref='payment_method', lazy='dynamic')

    def __repr__(self):
        return f'<PaymentMethod {self.name}>'

class UserActivity(db.Model):
    """سجل أنشطة المستخدمين"""
    __tablename__ = 'user_activities'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)
    description = db.Column(db.String(255))
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def get_action_display(self):
        """عرض النشاط بالعربية"""
        actions = {
            'login': 'تسجيل دخول',
            'logout': 'تسجيل خروج',
            'password_change': 'تغيير كلمة المرور',
            'user_create': 'إنشاء مستخدم',
            'user_update': 'تحديث مستخدم',
            'user_status_change': 'تغيير حالة مستخدم',
            'invoice_create': 'إنشاء فاتورة',
            'invoice_update': 'تحديث فاتورة',
            'invoice_send': 'إرسال فاتورة',
            'invoice_cancel': 'إلغاء فاتورة',
            'customer_create': 'إنشاء عميل',
            'customer_update': 'تحديث عميل',
            'payment_create': 'إنشاء دفعة',
            'backup_create': 'إنشاء نسخة احتياطية',
            'backup_restore': 'استعادة نسخة احتياطية'
        }
        return actions.get(self.action, self.action)

    def __repr__(self):
        return f'<UserActivity {self.action}>'

# Event Listeners
@event.listens_for(InvoiceItem, 'before_insert')
@event.listens_for(InvoiceItem, 'before_update')
def calculate_invoice_item_total(mapper, connection, target):
    """حساب إجمالي عنصر الفاتورة تلقائياً"""
    target.calculate_total()

@event.listens_for(InvoiceItem, 'after_insert')
@event.listens_for(InvoiceItem, 'after_update')
@event.listens_for(InvoiceItem, 'after_delete')
def update_invoice_totals(mapper, connection, target):
    """تحديث إجماليات الفاتورة عند تغيير العناصر"""
    if hasattr(target, 'invoice') and target.invoice:
        target.invoice.calculate_totals()

def init_default_data():
    """إنشاء البيانات الافتراضية"""

    # إنشاء المستخدم الافتراضي
    if not User.query.filter_by(username='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            full_name='مدير النظام',
            role='admin'
        )
        admin.set_password('admin123')
        db.session.add(admin)

    # إنشاء طرق الدفع الافتراضية
    default_payment_methods = [
        {'name': 'نقداً', 'type': 'cash'},
        {'name': 'تحويل بنكي', 'type': 'bank'},
        {'name': 'بطاقة ائتمان', 'type': 'card'},
        {'name': 'دفع إلكتروني', 'type': 'online'}
    ]

    for method_data in default_payment_methods:
        if not PaymentMethod.query.filter_by(name=method_data['name']).first():
            method = PaymentMethod(**method_data)
            db.session.add(method)

    # إنشاء الفئات الافتراضية
    default_categories = [
        {'name': 'مبيعات', 'type': 'income'},
        {'name': 'خدمات', 'type': 'income'},
        {'name': 'إيرادات أخرى', 'type': 'income'},
        {'name': 'مصروفات تشغيلية', 'type': 'expense'},
        {'name': 'رواتب ومكافآت', 'type': 'expense'},
        {'name': 'إيجارات', 'type': 'expense'},
        {'name': 'مصروفات أخرى', 'type': 'expense'}
    ]

    for category_data in default_categories:
        if not Category.query.filter_by(name=category_data['name']).first():
            category = Category(**category_data)
            db.session.add(category)

    db.session.commit()
